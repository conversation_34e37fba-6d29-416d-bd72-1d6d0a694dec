import 'dart:async';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get_it/get_it.dart';
import 'package:location/location.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/services/location_service.dart';
import 'package:storetrack_app/core/services/timer_service.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/models/profile_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';

class CompleteTaskView extends StatefulWidget {
  final entities.TaskDetail task;
  final VoidCallback? onSubmit;
  const CompleteTaskView({super.key, required this.task, this.onSubmit});

  @override
  State<CompleteTaskView> createState() => CompleteTaskViewState();
}

class CompleteTaskViewState extends State<CompleteTaskView> {
  final TextEditingController _commentsController = TextEditingController();
  final TextEditingController _actualMinutesController =
      TextEditingController();
  int _pagesPrinted = 0;
  int _claimableKMs = 0;
  bool _isTimerDurationUsed = false;
  Timer? _timer;

  // Budget calculation
  int _displayBudget = 0;

  // Services
  late final LocationService _locationService;
  late final SyncService _syncService;
  late final RealmDatabase _realmDatabase;
  late final TimerService _timerService;

  // Getters to expose form data
  String get comments => _commentsController.text;
  int get actualMinutes => int.tryParse(_actualMinutesController.text) ?? 0;
  int get pagesPrinted => _pagesPrinted;
  int get claimableKMs => _claimableKMs;
  bool get isTimerDurationUsed => _isTimerDurationUsed;
  bool get isSubmitting => _syncService.isSyncing.value;

  @override
  void dispose() {
    _commentsController.dispose();
    _actualMinutesController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _pagesPrinted = widget.task.pages?.toInt() ?? 0;
    _claimableKMs = widget.task.claimableKms?.toInt() ?? 0;

    // Initialize comments with existing task comment
    if (widget.task.comment != null && widget.task.comment!.isNotEmpty) {
      _commentsController.text = widget.task.comment!;
    }

    // Initialize services
    _locationService = GetIt.instance<LocationService>();
    _syncService = GetIt.instance<SyncService>();
    _realmDatabase = GetIt.instance<RealmDatabase>();
    _timerService = GetIt.instance<TimerService>();

    // Auto-populate actual minutes from timer if available
    _initializeTimerDuration();

    // Calculate and set display budget
    _initializeBudget();
  }

  // Calculate and set the display budget
  void _initializeBudget() {
    try {
      if (widget.task.taskId != null) {
        final budgetResult =
            FormUtils.calculateBudget(widget.task.taskId!.toInt());
        _displayBudget = budgetResult?.budgetHasChanged == true
            ? budgetResult!.tempBudget.toInt()
            : (widget.task.budget ?? 0).toInt();
      } else {
        _displayBudget = (widget.task.budget ?? 0).toInt();
      }
    } catch (e) {
      _displayBudget = (widget.task.budget ?? 0).toInt();
    }
  }

  // Calculate timer duration and populate actual minutes field
  Future<void> _initializeTimerDuration() async {
    try {
      logger('INIT_DURATION_START: taskId=${widget.task.taskId}');

      if (widget.task.taskId == null) {
        logger('INIT_DURATION_EXIT: taskId is null');
        return;
      }

      // Fetch fresh task data from database to get latest timer information
      final freshTaskData =
          await _timerService.getTaskById(widget.task.taskId!.toInt());
      if (freshTaskData == null) {
        logger('INIT_DURATION_EXIT: freshTaskData is null');
        return;
      }

      final commencement = freshTaskData.taskCommencementTimeStamp;
      final stopped = freshTaskData.taskStoppedTimeStamp;

      logger(
          'INIT_DURATION_TIMESTAMPS: commencement=$commencement, stopped=$stopped');
      logger(
          'INIT_DURATION_CURRENT_VALUE: currentText="${_actualMinutesController.text}", savedMinutes=${widget.task.minutes}');

      // Helper function to check if timestamp is valid (not 0001-01-01)
      bool isValidTimestamp(DateTime? timestamp) {
        return timestamp != null && timestamp.year > 1900;
      }

      logger(
          'INIT_DURATION_VALIDATION: commencementValid=${isValidTimestamp(commencement)}, stoppedValid=${isValidTimestamp(stopped)}');

      // Only populate if field is empty
      if (_actualMinutesController.text.isEmpty) {
        // If both commencement AND stopped timestamps exist and are valid, calculate from timer
        if (isValidTimestamp(commencement) && isValidTimestamp(stopped)) {
          logger(
              'INIT_DURATION_USING: timer calculation (both timestamps exist)');
          final duration = _timerService.calculateElapsedTime(freshTaskData);
          logger(
              'INIT_DURATION_CALCULATED: ${duration.inMinutes}m ${duration.inSeconds}s');

          if (duration.inMinutes >= 1) {
            _actualMinutesController.text = duration.inMinutes.toString();
            logger(
                'INIT_DURATION_SET: ${duration.inMinutes} minutes from timer');
          } else if (duration.inSeconds > 0) {
            _actualMinutesController.text = "${duration.inSeconds}s";
            logger('INIT_DURATION_SET: ${duration.inSeconds}s from timer');
          } else {
            logger('INIT_DURATION_SKIP: duration is zero');
          }
          _isTimerDurationUsed = true;
        } else {
          // Otherwise, use existing saved minutes (getTaskDuration equivalent)
          logger('INIT_DURATION_USING: saved minutes (missing timestamps)');
          final savedMinutes = widget.task.minutes?.toInt() ?? 0;
          logger('INIT_DURATION_SAVED: $savedMinutes minutes');

          if (savedMinutes > 0) {
            _actualMinutesController.text = savedMinutes.toString();
            logger('INIT_DURATION_SET: $savedMinutes minutes from saved data');
            _isTimerDurationUsed = false;
          } else {
            logger('INIT_DURATION_SKIP: no saved minutes available');
          }
        }
      } else {
        logger(
            'INIT_DURATION_SKIP: field already has value "${_actualMinutesController.text}"');
      }

      // Start periodic updates for in-progress tasks
      if (isValidTimestamp(commencement) && !isValidTimestamp(stopped)) {
        logger(
            'INIT_DURATION_TIMER: starting periodic updates for in-progress task');
        _startTimerForInProgressTask();
      }

      logger(
          'INIT_DURATION_COMPLETE: final value="${_actualMinutesController.text}", timerUsed=$_isTimerDurationUsed');
    } catch (e) {
      logger('INIT_DURATION_ERROR: ${e.toString()}');
      // Silently fail - timer duration is optional
    }
  }

  void _startTimerForInProgressTask() {
    _timer?.cancel();

    // Only start timer if task is in progress (commenced but not stopped)
    if (widget.task.taskCommencementTimeStamp != null &&
        widget.task.taskStoppedTimeStamp == null) {
      _timer = Timer.periodic(const Duration(minutes: 1), (timer) async {
        if (mounted && widget.task.taskId != null) {
          try {
            // Fetch fresh task data from database to get latest timer information
            final freshTaskData =
                await _timerService.getTaskById(widget.task.taskId!.toInt());
            if (freshTaskData == null) return;

            // Use TimerService to calculate elapsed time accounting for pauses with fresh data
            final duration = _timerService.calculateElapsedTime(freshTaskData);

            // Update only if user hasn't manually edited the field
            if (_isTimerDurationUsed && duration.inSeconds > 0) {
              String displayValue;
              if (duration.inMinutes >= 1) {
                // Display minutes for durations >= 1 minute
                displayValue = duration.inMinutes.toString();
              } else {
                // Display seconds for sub-minute durations
                displayValue = "${duration.inSeconds}s";
              }

              setState(() {
                _actualMinutesController.text = displayValue;
              });
            }
          } catch (e) {
            // Silently handle errors in periodic update
          }
        }
      });
    }
  }

  // Helper method to parse task duration handling both minutes and seconds format
  int _parseTaskDuration(String input) {
    if (input.isEmpty) return 0;

    // Handle seconds format (e.g., "4s")
    if (input.endsWith('s')) {
      final secondsStr = input.substring(0, input.length - 1);
      final seconds = int.tryParse(secondsStr) ?? 0;
      // Round up any seconds to at least 1 minute
      return seconds > 0 ? 1 : 0;
    }

    // Handle plain number as minutes
    return int.tryParse(input) ?? 0;
  }

  // Public submission method that can be called externally
  Future<void> submitTask() async {
    if (_syncService.isSyncing.value) return;

    // Set global sync state immediately for instant loader
    _syncService.isSyncing.value = true;

    logger('Task submission started for task ID: ${widget.task.taskId}');

    try {
      // Get task duration from UI using robust parsing
      final taskDuration = _parseTaskDuration(_actualMinutesController.text);
      logger(
          'Task duration parsed: $taskDuration minutes from input: "${_actualMinutesController.text}"');

      // Step 1: Validate forms
      final formsValidated = _checkFormsValidated();

      // Step 2: Validate photos
      final photosValidated = _checkPhotosValidated();
      logger('Photo validation result: $photosValidated');

      // Step 3: Check time/budget validation
      final timeWarning = await _checkTimeValidation(taskDuration);
      if (timeWarning != null) {
        logger('Time validation warning: $timeWarning');
      } else {
        logger('Time validation passed - no warnings');
      }

      // Handle validation errors
      if (!photosValidated) {
        logger('Task submission blocked - photo validation failed');
        _syncService.isSyncing.value = false;
        _showErrorDialog("Error!",
            "You must upload all required photos to complete this task.");
        return;
      }

      // Handle forms validation
      if (!(await formsValidated) &&
          (_commentsController.text.isEmpty ||
              _commentsController.text == "Enter comments...")) {
        logger(
            'Task submission blocked - forms validation failed and no comments provided');
        _syncService.isSyncing.value = false;
        _showErrorDialog("Error!",
            "You must complete all mandatory forms or add a comment to complete this task.");
        return;
      }

      // Handle time validation warnings
      if (timeWarning != null) {
        _syncService.isSyncing.value = false;
        final shouldContinue =
            await _showWarningDialog("Warning!", timeWarning);
        if (!shouldContinue) {
          logger(
              'Task submission cancelled by user due to time validation warning');
          return;
        }

        // Check if this is a blocking warning (org 8 or missing comment)
        if (timeWarning.contains("forbidden") ||
            timeWarning.contains("mention the reason")) {
          logger('Task submission blocked by time validation warning');
          return;
        }

        // User chose to continue, set loading state back to true
        _syncService.isSyncing.value = true;
      }

      // All validations passed
      logger('All validations passed, starting submission process');

      // Determine task success status
      bool isSuccessful = await formsValidated;
      logger('Task success status determined: $isSuccessful');

      // Get current position for submission
      LocationData? location;
      try {
        location = await _locationService.getCurrentPosition();
        logger(
            'Location acquired successfully: lat=${location?.latitude}, lng=${location?.longitude}');
      } catch (e) {
        logger('Failed to acquire location: ${e.toString()}');
      }

      // Update task in database
      logger(
          'Updating task in database with duration: $taskDuration, success: $isSuccessful');
      await _updateTaskInDatabase(
        isSuccessful: isSuccessful,
        taskDuration: taskDuration,
        latitude: location?.latitude,
        longitude: location?.longitude,
      );
      logger('Task database update completed');

      // Show success message
      if (mounted) {
        SnackBarService.success(
          context: context,
          message: 'Task submitted successfully!',
        );
      }

      // Trigger sync if network available
      logger('Starting sync process');
      if (mounted) {
        await _syncService.sync(context: context, force: true);
        logger('Sync process completed');
      }

      // Call the callback if provided
      if (widget.onSubmit != null) {
        logger('Executing onSubmit callback');
        widget.onSubmit!();
      }

      logger(
          'Task submission completed successfully for task ID: ${widget.task.taskId}');
    } catch (e) {
      logger('Task submission failed with error: ${e.toString()}');
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error submitting task: ${e.toString()}',
        );
      }
    } finally {
      _syncService.isSyncing.value = false;
      logger(
          'Task submission process finalized, global sync state reset to false');
    }
  }

  // Form validation - simplified
  Future<bool> _checkFormsValidated() async {
    int taskTotalFormsCount = 0;
    bool taskFormsValidated = false;
    int totalMandatoryFormCount = 0;
    int totalMandatoryFormCompleteCount = 0;

    if (widget.task.forms != null) {
      for (final formEntity in widget.task.forms!) {
        if (formEntity.isVisionForm != true) {
          taskTotalFormsCount++;

          if (formEntity.isMandatory == true) {
            totalMandatoryFormCount++;
          }

          if ((formEntity.formCompleted == true) &&
              (formEntity.questionAnswers != null) &&
              (formEntity.questionAnswers!.isNotEmpty)) {
            if (formEntity.isMandatory == true) {
              totalMandatoryFormCompleteCount++;
            }
          }
        }
      }
    }

    var mandatoryFormsProgress = await FormUtils.getMandatoryFormsProgress(
      taskId: widget.task.taskId!.toInt(),
    );

    if (mandatoryFormsProgress.totalVisible ==
        mandatoryFormsProgress.totalCompleted) {
      taskFormsValidated = true;
    }

    if ((widget.task.forms == null) || (taskTotalFormsCount == 0)) {
      taskFormsValidated = true;
    } else {
      if (totalMandatoryFormCount == totalMandatoryFormCompleteCount) {
        taskFormsValidated = true;
      }
    }

    logger('Forms validated: $taskFormsValidated');
    return taskFormsValidated;
  }

  // Photo validation
  bool _checkPhotosValidated() {
    if (widget.task.photoFolder == null || widget.task.photoFolder!.isEmpty) {
      return true; // No photo requirements
    }

    // Check if task has MPT forms (vision forms)
    bool hasMPT = false;
    if (widget.task.forms != null) {
      for (final form in widget.task.forms!) {
        if (form.isVisionForm == true) {
          hasMPT = true;
          break;
        }
      }
    }

    // MPT tasks don't need separate photo validation
    if (hasMPT) {
      return true;
    }

    // Check non-MPT photo requirements
    for (final photoFolder in widget.task.photoFolder!) {
      int photoCount = 0;
      if (photoFolder.photos != null) {
        for (final photo in photoFolder.photos!) {
          if (photo.userDeletedPhoto != true ||
              photo.cannotUploadMandatory == true) {
            photoCount++;
          }
        }

        if (photoFolder.attribute == true && photoCount == 0) {
          return false; // Mandatory folder has no photos
        }
      }
    }

    return true;
  }

  // Time/Budget validation with warnings
  Future<String?> _checkTimeValidation(int taskDuration) async {
    // Get user profile for org validation
    final realm = _realmDatabase.realm;

    // Get actual user ID from DataManager
    final dataManager = GetIt.instance<DataManager>();
    final actualUserId = await dataManager.getUserId() ?? "0";

    // Query with actual user ID (convert to int)
    final userProfile = realm.query<ProfileModel>(
        'userId == \$0', [int.parse(actualUserId)]).firstOrNull;
    final userOrgsId = userProfile?.orgIDs;

    // Calculate budget
    final budgetResult = FormUtils.calculateBudget(widget.task.taskId!.toInt());
    final taskBudgetCalculated = budgetResult?.tempBudget ?? 0;

    String? warningMessage;

    if (taskDuration == 0 && taskBudgetCalculated > 0) {
      warningMessage =
          "You entered 0 working hours or minutes, so you will not be paid for this task.";
    } else if (taskDuration > taskBudgetCalculated) {
      if (userOrgsId != null) {
        bool userFallsUnder6or1 =
            userOrgsId.contains("1") || userOrgsId.contains("6");
        if (userFallsUnder6or1 &&
            (_commentsController.text.isEmpty ||
                _commentsController.text == "Enter comments...")) {
          return "Please mention the reason for going over budget in the schedule comment section.";
        } else if (userOrgsId.contains("8")) {
          return "Reporting over budget is forbidden.";
        }
      } else {
        warningMessage =
            "Your claimed time exceeds the budgeted time for this visit.";
      }
    }

    // Check if forms are not validated but has comments
    final formsValidated = await _checkFormsValidated();
    if (!formsValidated &&
        _commentsController.text.isNotEmpty &&
        _commentsController.text != "Enter comments...") {
      if (warningMessage?.isEmpty ?? true) {
        warningMessage =
            "If you press OK, the task will be marked as unsuccessful. Press Cancel if you wish to continue working on this task.";
      } else {
        warningMessage =
            "Warning: $warningMessage\n\nAdditionally, if you press OK, the task will be marked as unsuccessful. Press Cancel if you wish to continue working on this task.";
      }
    }

    return warningMessage;
  }

  // Update task in database
  Future<void> _updateTaskInDatabase({
    required bool isSuccessful,
    required int taskDuration,
    double? latitude,
    double? longitude,
  }) async {
    final realm = _realmDatabase.realm;

    realm.write(() {
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.task.taskId]).firstOrNull;

      if (taskModel != null) {
        // Update task status
        taskModel.taskStatus = isSuccessful ? "Successful" : "Unsuccessful";

        // Update submission state
        final isTaskCompleted = taskModel.taskStatus == "Successful" ||
            taskModel.taskStatus == "Unsuccessful";
        if (isTaskCompleted) {
          // Check if all forms are completed for resubmit logic
          final formsCompleted = widget.task.forms?.every((form) =>
                  form.isVisionForm == true || form.formCompleted == true) ??
              true;

          if (formsCompleted) {
            taskModel.submissionState = 4; // RESUBMIT_CHANGE
          } else {
            taskModel.submissionState = 3; // RESUBMIT_NO_CHANGE
          }
        } else {
          taskModel.submissionState = 2; // FIRST_SUBMIT
        }

        // Update duration and other fields
        taskModel.minutes = taskDuration;
        taskModel.claimableKms = _claimableKMs;
        taskModel.pages = _pagesPrinted;
        taskModel.comment = _commentsController.text.isNotEmpty
            ? _commentsController.text
            : null;

        // Update location if available
        if (latitude != null && longitude != null) {
          taskModel.taskLatitude = latitude;
          taskModel.taskLongitude = longitude;
        }

        // Update timestamps
        taskModel.submissionTimeStamp = DateTime.now();

        if (taskModel.isOpen == true) {
          taskModel.scheduledTimeStamp = DateTime.now();
        }

        // Mark as sync pending
        taskModel.syncPending = true;
      }
    });
  }

  // Show error dialog
  void _showErrorDialog(String title, String message) {
    ConfirmDialog.show(
      context: context,
      title: title,
      message: message,
      confirmText: 'OK',
      cancelText: '',
      onConfirm: () {},
    );
  }

  // Show warning dialog with OK/Cancel
  Future<bool> _showWarningDialog(String title, String message) async {
    bool result = false;
    await ConfirmDialog.show(
      context: context,
      title: title,
      message: message,
      confirmText: 'OK',
      cancelText: 'Cancel',
      onConfirm: () {
        result = true;
      },
    );
    return result;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final bool isTaskSentToPayroll = widget.task.sentToPayroll == true;

    return Column(
      children: [
        // Show warning if task is sent to payroll
        if (isTaskSentToPayroll)
          Container(
            width: double.infinity,
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.orange.shade300),
            ),
            child: Row(
              children: [
                Icon(Icons.warning, color: Colors.orange.shade600),
                const Gap(8),
                Expanded(
                  child: Text(
                    "This task has been completed and sent to payroll. Task duration details cannot be edited, but comments can still be updated.",
                    style: textTheme.bodyMedium?.copyWith(
                      color: Colors.orange.shade900,
                    ),
                  ),
                ),
              ],
            ),
          ),
        SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: Colors.white,
            ),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Scheduled comments section
                Text(
                  'Scheduled comments',
                  style: textTheme.montserratFormsField,
                ),
                const Gap(16),
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: AppColors.blackTint2),
                    color: Colors.white,
                  ),
                  child: TextFormField(
                    controller: _commentsController,
                    maxLines: 3,
                    enabled: true,
                    decoration: InputDecoration(
                      hintText: 'Enter comments...',
                      hintStyle: textTheme.montserratFormsField.copyWith(
                        color: AppColors.blackTint1,
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16.0),
                    ),
                    style: textTheme.bodyLarge?.copyWith(
                      color: AppColors.black,
                      fontSize: 16,
                    ),
                  ),
                ),
                const Gap(24),
                // Budgeted minutes section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Budgeted minutes',
                      style: textTheme.montserratFormsField,
                    ),
                    Row(
                      children: [
                        const Icon(
                          Icons.access_time,
                          color: AppColors.blackTint1,
                          size: 20,
                        ),
                        const Gap(8),
                        Text(
                          '${_displayBudget}m',
                          style: textTheme.montserratTitleExtraSmall,
                        ),
                      ],
                    ),
                  ],
                ),
                const Gap(16),

                // Actual Minutes section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Actual minutes',
                          style: textTheme.montserratFormsField,
                        ),
                      ],
                    ),
                    Container(
                      width: 120,
                      height: 48,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(color: AppColors.blackTint2),
                        // color: AppColors.lightGrey1,
                      ),
                      child: TextFormField(
                        controller: _actualMinutesController,
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        enabled: !isTaskSentToPayroll,
                        onChanged: (value) {
                          // Reset timer indicator when user manually edits
                          if (_isTimerDurationUsed) {
                            setState(() {
                              _isTimerDurationUsed = false;
                            });
                          }
                        },
                        decoration: InputDecoration(
                          hintText: '0',
                          hintStyle: textTheme.montserratTitleExtraSmall,
                          border: InputBorder.none,
                          contentPadding:
                              const EdgeInsets.symmetric(horizontal: 12.0),
                        ),
                        style: textTheme.titleMedium?.copyWith(
                          color: isTaskSentToPayroll
                              ? AppColors.blackTint1
                              : AppColors.black,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const Gap(16),

                // Pages printed section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Pages printed',
                      style: textTheme.montserratFormsField,
                    ),
                    _buildCounterWidget(_pagesPrinted, (value) {
                      setState(() {
                        _pagesPrinted = value;
                      });
                    }, enabled: !isTaskSentToPayroll),
                  ],
                ),
                const Gap(16),

                // Claimable KMs section
                if (widget.task.showKm == true)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Claimable KMs',
                        style: textTheme.montserratFormsField,
                      ),
                      _buildCounterWidget(_claimableKMs, (value) {
                        setState(() {
                          _claimableKMs = value;
                        });
                      }, enabled: !isTaskSentToPayroll),
                    ],
                  ),
              ],
            ),
          ),
        ),
        const Gap(88),
      ],
    );
  }

  Widget _buildCounterWidget(int value, Function(int) onChanged,
      {bool enabled = true}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Minus button
        InkWell(
          onTap: enabled && value > 0 ? () => onChanged(value - 1) : null,
          borderRadius: BorderRadius.circular(10.0),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.blackTint2),
              borderRadius: BorderRadius.circular(10.0),
              color: enabled ? Colors.white : AppColors.lightGrey1,
            ),
            child: Icon(
              Icons.remove,
              color:
                  enabled && value > 0 ? AppColors.black : AppColors.blackTint1,
              size: 16,
            ),
          ),
        ),
        // Value display
        Container(
          width: 32,
          height: 48,
          margin: const EdgeInsets.symmetric(horizontal: 12),
          child: Center(
            child: Text(
              value.toString(),
              style: Theme.of(context)
                  .textTheme
                  .montserratTitleExtraSmall
                  .copyWith(
                    color: enabled ? AppColors.black : AppColors.blackTint1,
                  ),
            ),
          ),
        ),
        // Plus button
        InkWell(
          onTap: enabled ? () => onChanged(value + 1) : null,
          borderRadius: BorderRadius.circular(10.0),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.blackTint2),
              borderRadius: BorderRadius.circular(10.0),
              color: enabled ? Colors.white : AppColors.lightGrey1,
            ),
            child: Icon(
              Icons.add,
              color: enabled ? AppColors.black : AppColors.blackTint1,
              size: 16,
            ),
          ),
        ),
      ],
    );
  }
}
